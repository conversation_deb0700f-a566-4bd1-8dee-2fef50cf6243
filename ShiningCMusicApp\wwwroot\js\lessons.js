// Lessons page JavaScript functionality
// Custom QuickInfo popup and lesson management functions

window.showCustomQuickInfo = function(htmlContent, title) {
    // Create a modal-like overlay that matches Syncfusion's quick info styling
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        z-index: 1060;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        box-sizing: border-box;
    `;

    const popup = document.createElement('div');
    popup.style.cssText = `
        background: white;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        max-width: 350px;
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
        padding: 20px;
        border: 1px solid #e0e0e0;
        animation: quickInfoSlideIn 0.2s ease-out;
    `;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.cssText = `
        position: absolute;
        top: 8px;
        right: 12px;
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #666;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
    `;

    closeButton.onmouseover = () => closeButton.style.backgroundColor = '#f0f0f0';
    closeButton.onmouseout = () => closeButton.style.backgroundColor = 'transparent';

    const content = document.createElement('div');
    content.innerHTML = htmlContent;

    popup.appendChild(closeButton);
    popup.appendChild(content);
    overlay.appendChild(popup);

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes quickInfoSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    `;
    document.head.appendChild(style);

    // Close handlers
    const closeModal = () => {
        popup.style.animation = 'quickInfoSlideOut 0.15s ease-in';
        setTimeout(() => {
            if (document.body.contains(overlay)) {
                document.body.removeChild(overlay);
            }
            if (document.head.contains(style)) {
                document.head.removeChild(style);
            }
        }, 150);
    };

    closeButton.onclick = closeModal;
    overlay.onclick = (e) => {
        if (e.target === overlay) closeModal();
    };

    // Add escape key handler
    const escapeHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);

    document.body.appendChild(overlay);
};


// Global functions for edit and delete buttons
window.editLesson = function(lessonId) {
    // Close the quick info popup first
    const overlay = document.querySelector('div[style*="position: fixed"][style*="z-index: 1060"]');
    if (overlay) {
        overlay.remove();
    }

    // Call Blazor method to edit lesson
    DotNet.invokeMethodAsync('ShiningCMusicApp', 'EditLessonFromQuickInfo', lessonId);
};

window.deleteLesson = function(lessonId) {
    // Close the quick info popup first
    const overlay = document.querySelector('div[style*="position: fixed"][style*="z-index: 1060"]');
    if (overlay) {
        overlay.remove();
    }

    // Call Blazor method to delete lesson
    DotNet.invokeMethodAsync('ShiningCMusicApp', 'DeleteLessonFromQuickInfo', lessonId);
};

// Fix QuickInfo popup spacing and adaptive behavior
window.fixQuickInfoPopup = function(isAdmin) {
    setTimeout(() => {
        const popup = document.querySelector('.e-quick-popup-wrapper');
        if (popup) {
            // Remove adaptive/device classes that cause full-screen behavior
            popup.classList.remove('e-device', 'e-adaptive');

            // Set auto height and max-height to prevent extra space
            const popupElement = popup.querySelector('.e-event-popup');
            if (popupElement) {
                popupElement.style.height = 'auto';
                popupElement.style.maxHeight = '80vh';
                popupElement.style.minHeight = 'auto';
            }

            // Ensure content area fits content tightly
            const contentElement = popup.querySelector('.e-popup-content');
            if (contentElement) {
                contentElement.style.height = 'auto';
                contentElement.style.minHeight = 'auto';
                contentElement.style.paddingBottom = '16px';
            }

            // Hide footer for non-admin users on desktop (mobile footer is hidden via CSS)
            let footerElement = popup.querySelector('.e-popup-footer');
            if (footerElement && !isAdmin) {
                // Check if we're on mobile (width < 992px to match CSS media query)
                const isMobile = window.innerWidth < 992;
                if (!isMobile) {
                    footerElement.style.setProperty('display', 'none', 'important');
                }
            }
        }
    }, 100);
};

// Function to scroll to recurrence section with visual feedback
window.scrollToRecurrenceSection = function() {
    const recurrenceSection = document.getElementById('recurrence-section');
    if (recurrenceSection) {
        // Scroll to the section
        recurrenceSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
        });

        // Add a subtle highlight effect
        recurrenceSection.style.transition = 'background-color 0.3s ease';
        recurrenceSection.style.backgroundColor = '#e3f2fd';

        setTimeout(() => {
            recurrenceSection.style.backgroundColor = '';
        }, 1500);
    }
};

// Initialize responsive behavior for tutor colors collapse
window.initializeTutorColorsResponsive = function() {
    const collapseElement = document.getElementById('tutorColorsCollapse');
    if (!collapseElement) return;

    // Function to handle responsive behavior
    function handleResponsiveCollapse() {
        const isDesktop = window.innerWidth >= 768; // md breakpoint

        if (isDesktop) {
            // Desktop: show by default
            if (!collapseElement.classList.contains('show')) {
                collapseElement.classList.add('show');
            }
        } else {
            // Mobile: hide by default (only if not manually opened)
            if (!collapseElement.hasAttribute('data-manually-opened')) {
                collapseElement.classList.remove('show');
            }
        }
    }

    // Track manual interactions
    const toggleButton = document.querySelector('[data-bs-target="#tutorColorsCollapse"]');
    if (toggleButton) {
        toggleButton.addEventListener('click', function() {
            // Mark as manually interacted
            setTimeout(() => {
                if (collapseElement.classList.contains('show')) {
                    collapseElement.setAttribute('data-manually-opened', 'true');
                } else {
                    collapseElement.removeAttribute('data-manually-opened');
                }
            }, 350); // Wait for Bootstrap animation
        });
    }

    // Initial setup
    handleResponsiveCollapse();

    // Listen for window resize
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResponsiveCollapse, 250);
    });
};
